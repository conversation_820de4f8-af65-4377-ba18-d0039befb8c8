<div ng-controller="StudentMainController as SMC" ng-init="SMC.init('registration')">
	<div ng-controller="StudentRegistrationController as SRC" ng-init="SRC.init()">
		<a-module>
			<a-container>
				<a-canvas>
					<a-header is-main="true">
						<a-row>
							<a-col size="4">

								<h4> <a-glyph icon="globe"></a-glyph> Online Registrations</h4>

							</a-col>
							
							<?php include('shared/filter_ui.php');?>
						</a-row>
					</a-header>
					<a-header>
						<a-row>
							
							<a-col size="3">
								<div class="btn-group" ng-class="{open:SMC.toggleExportDropdown}">
									<a-button type="success" ng-click="SMC.exportToExcel()" ng-disabled="SMC.ExportingFile">
										{{SMC.ExportingFile?'Exporting' :'Export' }} to Excel
									</a-button>
									<ul class="dropdown-menu">
										<li>
											<a  ng-click="SMC.openAdvanceExport()">Advance Export</a>	
										</li>
									</ul>

									<a-button type="success" ng-click="SMC.toggleExportDropdown=!SMC.toggleExportDropdown">
										<span class="caret"></span>
									</a-button>
								</div>
							</a-col>
							<a-col size="6">
								
								
							</a-col>
							<a-col size="3" align="right">
								<a-pager meta="SMC.Meta" on-navigate="SMC.gotoPage"></a-pager>
								
						
							</a-col>
						</a-row>
								
					</a-header>
					<a-content>
						<a-row>
							<a-col>
								<a-table headers="SMC.StuHeaders" props="SMC.StuProps" data="SMC.Students" ng-model="SMC.ActiveRecord"   on-row-click="SMC.openModal" is-preload="SMC.isLoading" ></a-table>
							</a-col>
						</a-row>
					</a-content>
				</a-canvas>
			</a-container>
		</a-module>
		<?php
			$_MODULE='registrations';
			include('shared/active_record.php');
		?>
		<a-modal id="AdvanceExportdModal" title="Advance Export" has-close="true">
			<a-modal-body>
				<a-row>
					<a-col size="4">
						<div class="form-group">
							<label for="">Registration</label>
							<div ng-repeat="regField in SMC.ExportFields.registration">
								<input type="checkbox" ng-disabled="SMC.ExportDefaults[regField]"ng-model="SMC.ExportFlags.registration[regField]" name="registration_field" />	{{regField}}
							</div>
						</div>
					</a-col>
					<a-col size="4">
						<div class="form-group">
							<label for="">Guardian</label>
							<div ng-repeat="grdField in SMC.ExportFields.guardian">
								<input type="checkbox" ng-model="SMC.ExportFlags.guardian[grdField]" name="guardian_field" />	{{grdField}}
							</div>
						</div>

					</a-col>
					<a-col size="4">
						<div class="form-group">
							<label for="">Household</label>
							<div ng-repeat="houField in SMC.ExportFields.household">
								<input type="checkbox" ng-model="SMC.ExportFlags.household[houField]" name="household_field" />	{{houField}}
							</div>
						</div>

						<div class="form-group">
							<label for="">Application Status</label>
							<div><input type="checkbox" name="status_field" ng-model="SMC.ExportFlags.status.ALL"> All</div>	
							<div ng-repeat="statusObj in SMC.AppStatuses|filter:{id:'!ALL'}">
								<input type="checkbox" ng-model="SMC.ExportFlags.status[statusObj.id]"  ng-disabled="SMC.ExportFlags.status.ALL" name="status_field" />	{{statusObj.name}}
							</div>
						</div>
					</a-col>
				</a-row>
				<a-row>
					<a-col size="6">
						<a-button type="default" ng-click="SMC.closeAdvanceExport()">
							Close
						</a-button>
						<a-button type="primary outline" ng-click="SMC.resetExportFlags()">
							Reset Filters
						</a-button>
					</a-col>
					<a-col size="6" align="right">
						<a-button type="success" ng-click="SMC.exportToExcel(SMC.ExportFlags)" ng-disabled="SMC.AdvanceExportingFile">
							{{SMC.AdvanceExportingFile?'Exporting' :'Advance Export' }} to Excel
						</a-button>
					</a-col>
				</a-row>
			</a-modal-body>
		</a-modal>

	</div>
</div>