# Advanced Export for Online Registrations

![Advanced Export Interface](md_files/images/advance_export.jpg)
*The Advanced Export modal allows customization of export fields and status filtering*

## Overview

The Advanced Export feature provides administrators with comprehensive data export capabilities for online registration records. This powerful tool allows you to customize which fields to include in your Excel exports, filter by application status, and generate detailed reports for analysis and record-keeping purposes.

## Available Options

### Export Categories

The Advanced Export feature organizes data into three main categories:

#### Registration Information
- **Default Fields** (always included):
  - Registration ID
  - Date Submitted
  - Student Name
  - Incoming Grade Level
  - Student Type
  - Amount Paid
  - Status

- **Additional Fields** (optional):
  - Student ID
  - Year Level
  - Notes
  - Updated Date
  - Attachment

#### Guardian Information
- Father's last name, first name, middle name
- Father's occupation and contact details (mobile, email)
- Mother's last name, first name, middle name
- Mother's occupation and contact details (mobile, email)
- Notification preferences for both parents

#### Household Information
- Street address
- Barangay
- City
- Province
- ZIP code

### Status Filtering Options

- **All**: Export all registration records regardless of status
- **Pending**: Only pending applications awaiting review
- **Validated**: Applications that have been document-verified
- **Approved**: Applications that have been confirmed and approved
- **Declined**: Applications that have been rejected

## How It Works

### Accessing Advanced Export

1. Navigate to the **Online Registrations** page in the admin panel
2. Locate the **Export to Excel** button in the header section
3. Click the **dropdown arrow** next to the Export button
4. Select **Advance Export** from the dropdown menu

### Customizing Your Export

1. **Select Registration Fields**: Choose additional registration fields beyond the default ones
2. **Choose Guardian Information**: Select which guardian details to include in your export
3. **Include Household Data**: Add address and location information as needed
4. **Filter by Status**: Choose specific application statuses or select "All" for complete data

### Generating the Export

1. Review your field selections in the Advanced Export modal
2. Use the **Reset Filters** button if you need to start over with default selections
3. Click **Advance Export to Excel** to generate your customized report
4. Wait for the file to be processed and automatically downloaded

## What If

### What if I need to export all data?
- Select "All" in the Application Status section
- Check all available fields in each category
- This will generate a comprehensive report with all available information

### What if I only need basic information?
- Use the standard **Export to Excel** button instead of Advanced Export
- This provides a quick export with default fields and payment information

### What if the export is taking too long?
- Large datasets may take several minutes to process
- Avoid closing the browser window while the export is generating
- Consider filtering by date range or status to reduce the dataset size

### What if I need to export filtered data?
- Use the search and filter options on the main registration page first
- The Advanced Export will respect any active filters or search criteria
- This allows you to export specific subsets of data

## Sample Export Structure

The exported Excel file will contain columns based on your selections:

| Default Columns | Optional Registration | Guardian Info | Household Info |
|----------------|----------------------|---------------|----------------|
| Date Submitted | Student ID | Father Last Name | Street |
| Ref No. | Year Level | Father Mobile | Barangay |
| Student Name | Notes | Mother Email | City |
| Incoming Grade Level | Updated Date | Mother Mobile | Province |
| Student Type | Attachment | Father Occupation | ZIP Code |
| Amount Paid | | Mother Occupation | |
| Status | | | |

## Benefits

- **Customizable Reports**: Include only the data you need for specific purposes
- **Comprehensive Analysis**: Access to all registration, guardian, and household information
- **Flexible Filtering**: Export data by application status or date range
- **Time-Saving**: Automated Excel generation eliminates manual data compilation
- **Data Integrity**: Ensures accurate and up-to-date information in exports

## Best Practices

- **Regular Exports**: Generate weekly or monthly reports for tracking registration trends
- **Filtered Exports**: Use status filters to create targeted reports for different departments
- **Data Security**: Handle exported files according to your institution's data privacy policies
- **File Organization**: Use descriptive filenames that include date ranges and filter criteria
- **Backup Purposes**: Regular exports serve as data backups for critical registration information

The Advanced Export feature streamlines administrative reporting and provides the flexibility needed for comprehensive registration data analysis.
